package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.EmployeeAttendanceService;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingRequest;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingResponse;
import com.stpl.tech.master.domain.model.EligibilityType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.EMPLOYEE_ATTENDANCE_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.USER_MANAGEMENT_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

/**
 * REST Controller for Employee Eligibility Mapping operations
 */
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + USER_MANAGEMENT_SERVICES_ROOT_CONTEXT +  SEPARATOR + EMPLOYEE_ATTENDANCE_ROOT_CONTEXT)
public class EmployeeAttendanceResource extends AbstractResources  {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeAttendanceResource.class);

    @Autowired
    private EmployeeAttendanceService employeeAttendanceService;


    @PostMapping(value = "/mappings",consumes = MediaType.APPLICATION_JSON_VALUE,produces = MediaType.APPLICATION_JSON_VALUE)
//    @ResponseStatus(HttpStatus.CREATED)
    public EmployeeEligibilityMappingResponse saveMapping(@Valid @RequestBody EmployeeEligibilityMappingRequest request,
            HttpServletRequest httpRequest) throws DataUpdationException {
        LOG.info("Request to save employee eligibility mapping for empId: {}, type: {}",
                request.getEmpId(), request.getEligibilityType());
        return employeeAttendanceService.saveEmployeeAttendanceMapping(request , String.valueOf(getLoggedInUser(httpRequest)));
    }

    /**
     * Get mappings by employee ID
     * @param empId employee ID
     * @return list of mappings
     */
    @GetMapping(value = "/mappings/{empId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    public List<EmployeeEligibilityMappingResponse> getAttendanceMappingsByEmpId(@PathVariable("empId") String empId) {
        LOG.info("Request to get mappings for empId: {}", empId);
        return employeeAttendanceService.getEligibilityAttendanceMappingsByEmpId(empId);
    }

    /**
     * Get mappings by employee ID and eligibility type
     * @param empId employee ID
     * @param eligibilityType eligibility type
     * @return list of mappings
     */
//    @GetMapping(value = "mappings/{empId}/{eligibilityType}", produces = MediaType.APPLICATION_JSON_VALUE)
//    @ResponseStatus(HttpStatus.OK)
//    public List<EmployeeEligibilityMappingResponse> getMappingsByEmpIdAndType(
//            @PathVariable String empId,
//            @PathVariable EligibilityType eligibilityType) {
//        LOG.info("Request to get mappings for empId: {} and type: {}", empId, eligibilityType);
//        return employeeAttendanceService.getMappingsByEmpIdAndType(empId, eligibilityType);
//    }

}

