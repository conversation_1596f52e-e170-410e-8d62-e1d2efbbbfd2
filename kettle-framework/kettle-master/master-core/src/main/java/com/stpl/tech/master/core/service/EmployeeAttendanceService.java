package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingRequest;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingResponse;
import com.stpl.tech.master.domain.model.EligibilityType;

import java.util.List;

/**
 * Service interface for Employee Eligibility Mapping operations
 */
public interface EmployeeAttendanceService {

    /**
     * Save employee eligibility mapping
     * @param request mapping request
     * @param createdBy user who created the mapping
     * @return saved mapping response
     * @throws com.stpl.tech.master.core.exception.DataUpdationException if save fails
     */

    EmployeeEligibilityMappingResponse saveEmployeeAttendanceMapping(EmployeeEligibilityMappingRequest request, String createdBy) throws DataUpdationException;

    /**
     * Get mappings by employee ID
     * @param empId employee ID
     * @return list of mappings
     */
    List<EmployeeEligibilityMappingResponse> getEligibilityAttendanceMappingsByEmpId(String empId);

    /**
     * Get mappings by employee ID and eligibility type
     * @param empId employee ID
     * @param eligibilityType eligibility type
     * @return list of mappings
     */
//    List<EmployeeEligibilityMappingResponse> getMappingsByEmpIdAndType(String empId, EligibilityType eligibilityType);
}
