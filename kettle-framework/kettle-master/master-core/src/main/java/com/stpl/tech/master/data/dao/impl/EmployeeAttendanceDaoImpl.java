package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.data.dao.EmployeeAttendanceDao;
import com.stpl.tech.master.data.model.EmployeeAttendance;
import com.stpl.tech.master.domain.model.EligibilityType;
import com.stpl.tech.master.domain.model.MappingType;
import com.stpl.tech.master.domain.model.SystemStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;

/**
 * DAO implementation for Employee Eligibility Mapping operations
 */
@Repository
public class EmployeeAttendanceDaoImpl extends AbstractMasterDaoImpl implements EmployeeAttendanceDao {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeAttendanceDaoImpl.class);


    @Override
    public List<EmployeeAttendance> findByEmpId(String empId) {
        try {
            Query query = manager.createQuery(
                "FROM EmployeeAttendance ea WHERE ea.empId = :empId");
            query.setParameter("empId", empId);
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error finding mappings by empId: {}", empId, e);
            throw e;
        }
    }

    @Override
    public List<EmployeeAttendance> findByEmpIdAndEligibilityType(String empId, EligibilityType eligibilityType) {
        try {
            Query query = manager.createQuery(
                "FROM EmployeeAttendance ea WHERE ea.empId = :empId AND ea.eligibilityType = :eligibilityType");
            query.setParameter("empId", empId);
            query.setParameter("eligibilityType", eligibilityType);
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error finding mappings by empId: {} and eligibilityType: {}", empId, eligibilityType, e);
            throw e;
        }
    }

    @Override
    public List<EmployeeAttendance> findByEmpIdValueAndType(String empId, String value , MappingType mappingType , EligibilityType eligibilityType) {
        try {
            Query query = manager.createQuery(
                "FROM EmployeeAttendance ea WHERE ea.empId = :empId AND ea.value = :value AND ea.mappingType = :mappingType AND ea.eligibilityType = :eligibilityType");
            query.setParameter("empId", empId);
            query.setParameter("value", value);
            query.setParameter("mappingType", mappingType);
            query.setParameter("eligibilityType", eligibilityType);
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error finding mappings by empId: {} , value: {} and mappingType: {}" , empId, value, mappingType,eligibilityType, e);
            throw e;
        }
    }

    @Override
    public List<EmployeeAttendance> findByEmpIdAndEligibilityTypeAndStatus(String empId,
                                                                           EligibilityType eligibilityType,
                                                                           SystemStatus status) {
        try {
            Query query = manager.createQuery(
                "FROM EmployeeAttendance ea WHERE ea.empId = :empId " +
                "AND ea.eligibilityType = :eligibilityType AND ea.status = :status");
            query.setParameter("empId", empId);
            query.setParameter("eligibilityType", eligibilityType);
            query.setParameter("status", status);
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error finding mappings by empId: {}, eligibilityType: {}, status: {}",
                     empId, eligibilityType, status, e);
            throw e;
        }
    }
}
