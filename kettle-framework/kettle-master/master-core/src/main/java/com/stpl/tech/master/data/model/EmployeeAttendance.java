package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import com.stpl.tech.master.domain.model.EligibilityType;
import com.stpl.tech.master.domain.model.MappingType;
import com.stpl.tech.master.domain.model.SystemStatus;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * JPA Entity for EMP_ELIGIBILITY_MAPPING table
 */
@Entity
@Table(name = "EMP_ELIGIBILITY_MAPPING")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeAttendance implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "CREATED_AT", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "CREATED_BY", length = 255)
    private String createdBy;

    @Enumerated(EnumType.STRING)
    @Column(name = "ELIGIBILITY_TYPE" , columnDefinition = "ENUM('ATTENDANCE','APPROVAL')", nullable = false)
    private EligibilityType eligibilityType;

    @Column(name = "EMP_ID", length = 255, nullable = false)
    private String empId;

    @Enumerated(EnumType.STRING)
    @Column(name = "MAPPING_TYPE" , columnDefinition = "ENUM('UNIT','CITY','REGION')", nullable = false)
    private MappingType mappingType;

    @Enumerated(EnumType.STRING)
    @Column(name = "STATUS" , columnDefinition = "ENUM('ACTIVE','IN_ACTIVE')", nullable = false)
    private SystemStatus status;

    @Column(name = "UPDATED_AT", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @Column(name = "UPDATED_BY", length = 255)
    private String updatedBy;

    @Column(name = "VALUE", length = 255, nullable = false)
    private String value;

//    @PrePersist
//    protected void onCreate() {
//        Date now = new Date();
//        createdAt = now;
//        updatedAt = now;
//        if (status == null) {
//            status = SystemStatus.ACTIVE;
//        }
//    }
//
//    @PreUpdate
//    protected void onUpdate() {
//        updatedAt = new Date();
//    }
}
