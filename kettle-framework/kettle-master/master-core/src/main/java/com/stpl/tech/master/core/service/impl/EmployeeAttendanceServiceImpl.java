package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.EmployeeAttendanceService;
import com.stpl.tech.master.data.dao.EmployeeAttendanceDao;
import com.stpl.tech.master.data.model.EmployeeAttendance;
import com.stpl.tech.master.domain.model.*;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service implementation for Employee Eligibility Mapping operations
 */
@Service
@Transactional
public class EmployeeAttendanceServiceImpl implements EmployeeAttendanceService {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeAttendanceServiceImpl.class);

    @Autowired
    private EmployeeAttendanceDao employeeAttendanceDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public EmployeeEligibilityMappingResponse saveEmployeeAttendanceMapping(EmployeeEligibilityMappingRequest request, String createdBy) throws DataUpdationException {
        // need improvment here what if i want to map to different units or rehions
        try {
            LOG.info("Saving employee eligibility mapping for empId: {}, type: {}, mappingType: {}, value: {}",
                    request.getEmpId(), request.getEligibilityType(), request.getMappingType(), request.getValue());
            List<EmployeeAttendance> existingMapping = employeeAttendanceDao.findByEmpIdValueAndType(request.getEmpId(),request.getValue(),request.getMappingType());
            if(!existingMapping.isEmpty()){
                throw new DataUpdationException("Mapping already exists for this employee id and value");
            } else {

                // Create entity from request
                EmployeeAttendance mapping = new EmployeeAttendance();
                mapping.setEligibilityType(request.getEligibilityType());
                mapping.setEmpId(request.getEmpId());
                mapping.setMappingType(request.getMappingType());
                mapping.setValue(request.getValue());
                mapping.setCreatedBy(createdBy);
                mapping.setUpdatedBy(createdBy);
                mapping.setStatus(SystemStatus.ACTIVE);
                mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
                mapping.setUpdatedAt(AppUtils.getCurrentTimestamp());

                // Save to database
                EmployeeAttendance savedMapping = employeeAttendanceDao.add(mapping);

                LOG.info("Successfully saved employee eligibility mapping with ID: {}", savedMapping.getId());

                // Convert to response
                return convertToResponse(savedMapping);
            }

        } catch (Exception e) {
            LOG.error("Error saving employee eligibility mapping", e);
            throw new DataUpdationException("Error saving employee eligibility mapping");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<EmployeeEligibilityMappingResponse> getEligibilityAttendanceMappingsByEmpId(String empId) {
        try {
            LOG.info("Getting mappings for empId: {}", empId);
            List<EmployeeAttendance> mappings = employeeAttendanceDao.findByEmpId(empId);
            return mappings.stream()
                    .map(this::convertToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOG.error("Error getting mappings for empId: {}", empId, e);
            throw e;
        }
    }

//    @Override
//    @Transactional(readOnly = true)
//    public List<EmployeeEligibilityMappingResponse> getMappingsByEmpIdAndType(String empId, EligibilityType eligibilityType) {
//        try {
//            LOG.info("Getting mappings for empId: {} and type: {}", empId, eligibilityType);
//            List<EmployeeAttendance> mappings = employeeAttendanceDao.findByEmpIdAndEligibilityType(empId, eligibilityType);
//            return mappings.stream()
//                    .map(this::convertToResponse)
//                    .collect(Collectors.toList());
//        } catch (Exception e) {
//            LOG.error("Error getting mappings for empId: {} and type: {}", empId, eligibilityType, e);
//            throw e;
//        }
//    }

    /**
     * Convert entity to response DTO
     * @param mapping entity
     * @return response DTO
     */
    private EmployeeEligibilityMappingResponse convertToResponse(EmployeeAttendance mapping) {
        EmployeeEligibilityMappingResponse response = new EmployeeEligibilityMappingResponse();
        response.setId(mapping.getId());
        response.setCreatedAt(mapping.getCreatedAt());
        response.setCreatedBy(mapping.getCreatedBy());
        response.setEligibilityType(mapping.getEligibilityType());
        response.setEmpId(mapping.getEmpId());
        response.setMappingType(mapping.getMappingType());
        response.setStatus(mapping.getStatus());
        response.setUpdatedAt(mapping.getUpdatedAt());
        response.setUpdatedBy(mapping.getUpdatedBy());
        response.setValue(mapping.getValue());
        return response;
    }
}
